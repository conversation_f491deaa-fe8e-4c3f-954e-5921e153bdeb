/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.config;

/** SFTP连接配置常量 包含Session和Channel相关的配置参数 */
public class SftpConnectionConfig {

    // 连接超时配置
    public static final String FS_SFTP_CONNECTION_TIMEOUT = "fs.sftp.connection.timeout";
    public static final String FS_SFTP_SESSION_TIMEOUT = "fs.sftp.session.timeout";
    public static final String FS_SFTP_SERVER_ALIVE_INTERVAL = "fs.sftp.server.alive.interval";
    public static final String FS_SFTP_SERVER_ALIVE_COUNT_MAX = "fs.sftp.server.alive.count.max";

    // Session和Channel配置
    public static final String FS_SFTP_MAX_CHANNELS_PER_SESSION =
            "fs.sftp.max.channels.per.session";
    public static final String FS_SFTP_SESSION_REUSE_ENABLED = "fs.sftp.session.reuse.enabled";

    // 重试配置
    public static final String FS_SFTP_MAX_RETRY_ATTEMPTS = "fs.sftp.max.retry.attempts";
    public static final String FS_SFTP_RETRY_DELAY_MS = "fs.sftp.retry.delay.ms";

    // 压缩配置
    public static final String FS_SFTP_COMPRESSION_ENABLED = "fs.sftp.compression.enabled";
    public static final String FS_SFTP_COMPRESSION_LEVEL = "fs.sftp.compression.level";

    // 默认值
    public static final int DEFAULT_CONNECTION_TIMEOUT = 30000; // 30秒
    public static final int DEFAULT_SESSION_TIMEOUT = 60000; // 60秒
    public static final int DEFAULT_SERVER_ALIVE_INTERVAL = 30000; // 30秒
    public static final int DEFAULT_SERVER_ALIVE_COUNT_MAX = 3;
    public static final int DEFAULT_MAX_CHANNELS_PER_SESSION = 10;
    public static final boolean DEFAULT_SESSION_REUSE_ENABLED = true;
    public static final int DEFAULT_MAX_RETRY_ATTEMPTS = 3;
    public static final long DEFAULT_RETRY_DELAY_MS = 1000;
    public static final boolean DEFAULT_COMPRESSION_ENABLED = true;
    public static final int DEFAULT_COMPRESSION_LEVEL = 6;

    private SftpConnectionConfig() {
        // 工具类，不允许实例化
    }
}
