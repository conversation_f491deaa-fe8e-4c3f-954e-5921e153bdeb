/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import org.apache.hadoop.util.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;

import java.io.IOException;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class SFTPConnectionPool {

    public static final Logger LOG = LoggerFactory.getLogger(SFTPFileSystem.class);

    // Connection timeout settings
    private static final int DEFAULT_CONNECTION_TIMEOUT = 30000; // 30 seconds
    private static final int DEFAULT_SESSION_TIMEOUT = 60000; // 60 seconds
    private static final int DEFAULT_SERVER_ALIVE_INTERVAL = 30000; // 30 seconds
    private static final int DEFAULT_SERVER_ALIVE_COUNT_MAX = 3; // Max missed heartbeats
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;

    // Session channel limits - JSch默认每个Session最多10个Channel
    private static final int DEFAULT_MAX_CHANNELS_PER_SESSION = 10;
    private static final String MAX_CHANNELS_CONFIG_KEY = "max_channels_per_session";

    // Maximum number of allowed live connections. This doesn't mean we cannot
    // have more live connections. It means that when we have more
    // live connections than this threshold, any unused connection will be
    // closed.
    private int maxConnection;
    private AtomicInteger liveConnectionCount = new AtomicInteger(0);
    private ConcurrentHashMap<ConnectionInfo, HashSet<ChannelSftp>> idleConnections =
            new ConcurrentHashMap<ConnectionInfo, HashSet<ChannelSftp>>();
    private ConcurrentHashMap<ChannelSftp, ConnectionInfo> con2infoMap =
            new ConcurrentHashMap<ChannelSftp, ConnectionInfo>();

    // Session管理 - 跟踪Session和其Channel数量
    private ConcurrentHashMap<Session, AtomicInteger> sessionChannelCount =
            new ConcurrentHashMap<Session, AtomicInteger>();
    private ConcurrentHashMap<ChannelSftp, Session> channel2SessionMap =
            new ConcurrentHashMap<ChannelSftp, Session>();

    SFTPConnectionPool(int maxConnection, int liveConnectionCount) {
        this.maxConnection = maxConnection;
        this.liveConnectionCount.set(liveConnectionCount);
    }

    synchronized ChannelSftp getFromPool(ConnectionInfo info) throws IOException {
        HashSet<ChannelSftp> cons = idleConnections.get(info);

        if (cons != null && cons.size() > 0) {
            synchronized (cons) {
                Iterator<ChannelSftp> it = cons.iterator();
                while (it.hasNext()) {
                    ChannelSftp channel = it.next();
                    it.remove(); // Remove only the current connection, not the entire set

                    // Validate connection before returning
                    if (isConnectionValid(channel)) {
                        return channel;
                    } else {
                        // Clean up invalid connection
                        cleanupInvalidConnection(channel);
                    }
                }

                // If no valid connections found, remove empty set
                if (cons.isEmpty()) {
                    idleConnections.remove(info);
                }
            }
        }
        return null;
    }

    synchronized void returnToPool(ChannelSftp channel) {
        if (channel == null || !isConnectionValid(channel)) {
            if (channel != null) {
                cleanupInvalidConnection(channel);
            }
            return;
        }

        ConnectionInfo info = con2infoMap.get(channel);
        if (info == null) {
            // Connection not tracked, close it
            try {
                closeConnection(channel);
            } catch (Exception e) {
                LOG.warn("Error closing untracked connection", e);
            }
            return;
        }

        HashSet<ChannelSftp> cons = idleConnections.get(info);
        if (cons == null) {
            cons = new HashSet<ChannelSftp>();
            idleConnections.put(info, cons);
        }

        synchronized (cons) {
            cons.add(channel);
        }
    }

    /** Validates if a connection is still valid and usable */
    private boolean isConnectionValid(ChannelSftp channel) {
        try {
            return channel != null
                    && channel.isConnected()
                    && channel.getSession() != null
                    && channel.getSession().isConnected()
                    && !channel.isClosed();
        } catch (Exception e) {
            LOG.debug("Connection validation failed", e);
            return false;
        }
    }

    /** Cleans up an invalid connection from tracking maps */
    private void cleanupInvalidConnection(ChannelSftp channel) {
        if (channel != null) {
            ConnectionInfo info = con2infoMap.remove(channel);
            Session session = channel2SessionMap.remove(channel);

            if (info != null) {
                liveConnectionCount.decrementAndGet();

                // Remove from idle connections if present
                HashSet<ChannelSftp> cons = idleConnections.get(info);
                if (cons != null) {
                    synchronized (cons) {
                        cons.remove(channel);
                        if (cons.isEmpty()) {
                            idleConnections.remove(info);
                        }
                    }
                }
            }

            // Update session channel count
            if (session != null) {
                AtomicInteger count = sessionChannelCount.get(session);
                if (count != null) {
                    int remaining = count.decrementAndGet();
                    if (remaining <= 0) {
                        sessionChannelCount.remove(session);
                        // Close session if no more channels
                        if (session.isConnected()) {
                            session.disconnect();
                            LOG.debug("Closed session with no remaining channels");
                        }
                    }
                }
            }

            // Force close the connection
            try {
                closeConnection(channel);
            } catch (Exception e) {
                LOG.debug("Error closing invalid connection", e);
            }
        }
    }

    /** Forcefully closes a connection */
    private void closeConnection(ChannelSftp channel) {
        if (channel != null) {
            try {
                if (channel.isConnected()) {
                    Session session = channel.getSession();
                    channel.disconnect();
                    if (session != null && session.isConnected()) {
                        session.disconnect();
                    }
                }
            } catch (Exception e) {
                LOG.debug("Error during connection close", e);
            }
        }
    }

    /** Shutdown the connection pool and close all open connections. */
    synchronized void shutdown() {
        if (this.con2infoMap == null) {
            return; // already shutdown in case it is called
        }
        LOG.info("Inside shutdown, con2infoMap size=" + con2infoMap.size());

        this.maxConnection = 0;
        Set<ChannelSftp> cons = con2infoMap.keySet();
        if (cons != null && cons.size() > 0) {
            // make a copy since we need to modify the underlying Map
            Set<ChannelSftp> copy = new HashSet<ChannelSftp>(cons);
            // Initiate disconnect from all outstanding connections
            for (ChannelSftp con : copy) {
                try {
                    disconnect(con);
                } catch (IOException ioe) {
                    ConnectionInfo info = con2infoMap.get(con);
                    if (info != null) {
                        LOG.error(
                                "Error encountered while closing connection to " + info.getHost(),
                                ioe);
                    }
                }
            }
        }
        // make sure no further connections can be returned.
        this.idleConnections.clear();
        this.con2infoMap.clear();
        this.channel2SessionMap.clear();
        this.sessionChannelCount.clear();
        this.liveConnectionCount.set(0);
    }

    public synchronized int getMaxConnection() {
        return maxConnection;
    }

    public synchronized void setMaxConnection(int maxConn) {
        this.maxConnection = maxConn;
    }

    public ChannelSftp connect(String host, int port, String user, String password, String keyFile)
            throws IOException {
        return connectWithRetry(host, port, user, password, keyFile, MAX_RETRY_ATTEMPTS);
    }

    /** 检查是否可以在现有Session上创建新的Channel */
    private ChannelSftp tryReuseExistingSession(ConnectionInfo info) throws IOException {
        // 查找具有相同连接信息的现有Session
        for (Map.Entry<Session, AtomicInteger> entry : sessionChannelCount.entrySet()) {
            Session session = entry.getKey();
            AtomicInteger channelCount = entry.getValue();

            // 检查Session是否有效且未达到Channel限制
            if (session.isConnected()
                    && channelCount.get() < DEFAULT_MAX_CHANNELS_PER_SESSION
                    && isSessionForConnectionInfo(session, info)) {

                try {
                    ChannelSftp channel = (ChannelSftp) session.openChannel("sftp");
                    channel.connect(DEFAULT_CONNECTION_TIMEOUT);

                    // 更新跟踪信息
                    con2infoMap.put(channel, info);
                    channel2SessionMap.put(channel, session);
                    channelCount.incrementAndGet();
                    liveConnectionCount.incrementAndGet();

                    LOG.debug(
                            "Reused existing session for {}@{}:{}, session channels: {}",
                            info.getUser(),
                            info.getHost(),
                            info.getPort(),
                            channelCount.get());

                    return channel;
                } catch (JSchException e) {
                    LOG.debug("Failed to create channel on existing session", e);
                    // 继续尝试其他Session或创建新Session
                }
            }
        }
        return null;
    }

    /** 检查Session是否属于指定的连接信息 */
    private boolean isSessionForConnectionInfo(Session session, ConnectionInfo info) {
        try {
            return session.getHost().equals(info.getHost())
                    && session.getPort() == info.getPort()
                    && session.getUserName().equals(info.getUser());
        } catch (Exception e) {
            return false;
        }
    }

    /** Connect with retry mechanism to handle transient network issues */
    private ChannelSftp connectWithRetry(
            String host, int port, String user, String password, String keyFile, int maxRetries)
            throws IOException {
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return connectInternal(host, port, user, password, keyFile);
            } catch (IOException e) {
                lastException = e;
                LOG.warn(
                        "Connection attempt {} failed for {}@{}:{}, error: {}",
                        attempt,
                        user,
                        host,
                        port,
                        e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Connection interrupted", ie);
                    }
                }
            }
        }

        throw new IOException("Failed to connect after " + maxRetries + " attempts", lastException);
    }

    private ChannelSftp connectInternal(
            String host, int port, String user, String password, String keyFile)
            throws IOException {
        // get connection from pool
        ConnectionInfo info = new ConnectionInfo(host, port, user);
        ChannelSftp channel = getFromPool(info);

        if (channel != null) {
            return channel; // Connection validation already done in getFromPool
        }

        // 尝试在现有Session上创建新Channel
        channel = tryReuseExistingSession(info);
        if (channel != null) {
            return channel;
        }

        // create a new session and connection
        JSch jsch = new JSch();
        Session session = null;
        try {
            if (user == null || user.length() == 0) {
                user = System.getProperty("user.name");
            }

            if (password == null) {
                password = "";
            }

            if (keyFile != null && keyFile.length() > 0) {
                jsch.addIdentity(keyFile);
            }

            if (port <= 0) {
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }

            session.setPassword(password);

            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");

            // Connection timeout configurations
            config.put("ConnectTimeout", String.valueOf(DEFAULT_CONNECTION_TIMEOUT));

            // Channel configurations - 增加每个Session的最大通道数
            config.put(
                    "max_channels_per_session", String.valueOf(DEFAULT_MAX_CHANNELS_PER_SESSION));

            // Compression settings for better performance
            config.put("compression.s2c", "<EMAIL>,zlib,none");
            config.put("compression.c2s", "<EMAIL>,zlib,none");
            config.put("compression_level", "6");

            session.setConfig(config);

            // Session timeout and keepalive settings
            session.setTimeout(DEFAULT_SESSION_TIMEOUT);
            session.setServerAliveInterval(DEFAULT_SERVER_ALIVE_INTERVAL);
            session.setServerAliveCountMax(DEFAULT_SERVER_ALIVE_COUNT_MAX);

            session.connect(DEFAULT_CONNECTION_TIMEOUT);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect(DEFAULT_CONNECTION_TIMEOUT);

            // Track the new connection and session
            con2infoMap.put(channel, info);
            channel2SessionMap.put(channel, session);

            // Track session channel count
            sessionChannelCount
                    .computeIfAbsent(session, k -> new AtomicInteger(0))
                    .incrementAndGet();
            liveConnectionCount.incrementAndGet();

            LOG.debug(
                    "Created new SFTP connection to {}@{}:{}, total connections: {}, session channels: {}",
                    user,
                    host,
                    port,
                    liveConnectionCount.get(),
                    sessionChannelCount.get(session).get());

            return channel;

        } catch (JSchException e) {
            // Clean up session if channel creation failed
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
            throw new IOException(
                    "Failed to create SFTP connection: " + StringUtils.stringifyException(e));
        }
    }

    void disconnect(ChannelSftp channel) throws IOException {
        if (channel == null) {
            return;
        }

        // Validate connection before deciding what to do
        if (!isConnectionValid(channel)) {
            cleanupInvalidConnection(channel);
            return;
        }

        // close connection if too many active connections
        boolean closeConnection = false;
        synchronized (this) {
            if (liveConnectionCount.get() > maxConnection) {
                con2infoMap.remove(channel);
                liveConnectionCount.decrementAndGet();
                closeConnection = true;
            }
        }

        if (closeConnection) {
            try {
                closeConnection(channel);
                LOG.debug(
                        "Closed excess connection, remaining connections: {}",
                        liveConnectionCount.get());
            } catch (Exception e) {
                LOG.warn("Error closing excess connection", e);
                throw new IOException("Error closing connection: " + e.getMessage(), e);
            }
        } else {
            returnToPool(channel);
        }
    }

    public int getIdleCount() {
        return this.idleConnections.size();
    }

    public int getLiveConnCount() {
        return this.liveConnectionCount.get();
    }

    public int getConnPoolSize() {
        return this.con2infoMap.size();
    }

    /**
     * Class to capture the minimal set of information that distinguish between different
     * connections.
     */
    static class ConnectionInfo {
        private String host = "";
        private int port;
        private String user = "";

        ConnectionInfo(String hst, int prt, String usr) {
            this.host = hst;
            this.port = prt;
            this.user = usr;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String hst) {
            this.host = hst;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int prt) {
            this.port = prt;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String usr) {
            this.user = usr;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }

            if (obj instanceof ConnectionInfo) {
                ConnectionInfo con = (ConnectionInfo) obj;

                boolean ret = true;
                if (this.host == null || !this.host.equalsIgnoreCase(con.host)) {
                    ret = false;
                }
                if (this.port >= 0 && this.port != con.port) {
                    ret = false;
                }
                if (this.user == null || !this.user.equalsIgnoreCase(con.user)) {
                    ret = false;
                }
                return ret;
            } else {
                return false;
            }
        }

        @Override
        public int hashCode() {
            int hashCode = 0;
            if (host != null) {
                hashCode += host.hashCode();
            }
            hashCode += port;
            if (user != null) {
                hashCode += user.hashCode();
            }
            return hashCode;
        }
    }
}
