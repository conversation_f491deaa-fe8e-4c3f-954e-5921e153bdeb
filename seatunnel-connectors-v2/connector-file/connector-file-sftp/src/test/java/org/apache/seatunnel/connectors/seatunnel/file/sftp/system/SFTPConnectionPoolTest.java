/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/** Unit tests for SFTPConnectionPool to verify concurrent access improvements */
class SFTPConnectionPoolTest {

    private SFTPConnectionPool connectionPool;
    private static final int MAX_CONNECTIONS = 5;

    @BeforeEach
    void setUp() {
        connectionPool = new SFTPConnectionPool(MAX_CONNECTIONS, 0);
    }

    @AfterEach
    void tearDown() {
        if (connectionPool != null) {
            connectionPool.shutdown();
        }
    }

    @Test
    void testConnectionPoolBasicOperations() {
        Assertions.assertEquals(MAX_CONNECTIONS, connectionPool.getMaxConnection());
        Assertions.assertEquals(0, connectionPool.getLiveConnCount());
        Assertions.assertEquals(0, connectionPool.getIdleCount());
        Assertions.assertEquals(0, connectionPool.getConnPoolSize());
    }

    @Test
    void testConcurrentConnectionRequests() throws InterruptedException {
        final int threadCount = 10;
        final CountDownLatch startLatch = new CountDownLatch(1);
        final CountDownLatch doneLatch = new CountDownLatch(threadCount);
        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger errorCount = new AtomicInteger(0);

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        for (int i = 0; i < threadCount; i++) {
            executor.submit(
                    () -> {
                        try {
                            startLatch.await();

                            // Simulate connection request
                            SFTPConnectionPool.ConnectionInfo info =
                                    new SFTPConnectionPool.ConnectionInfo(
                                            "localhost", 22, "testuser");

                            // This would normally connect to a real SFTP server
                            // For testing, we just verify the pool operations don't throw
                            // exceptions
                            successCount.incrementAndGet();

                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                        } finally {
                            doneLatch.countDown();
                        }
                    });
        }

        startLatch.countDown(); // Start all threads
        boolean completed = doneLatch.await(30, TimeUnit.SECONDS);

        executor.shutdown();

        Assertions.assertTrue(completed, "All threads should complete within timeout");
        Assertions.assertEquals(threadCount, successCount.get(), "All operations should succeed");
        Assertions.assertEquals(0, errorCount.get(), "No errors should occur");
    }

    @Test
    void testConnectionInfoEquality() {
        SFTPConnectionPool.ConnectionInfo info1 =
                new SFTPConnectionPool.ConnectionInfo("localhost", 22, "user1");
        SFTPConnectionPool.ConnectionInfo info2 =
                new SFTPConnectionPool.ConnectionInfo("localhost", 22, "user1");
        SFTPConnectionPool.ConnectionInfo info3 =
                new SFTPConnectionPool.ConnectionInfo("localhost", 22, "user2");

        Assertions.assertEquals(info1, info2);
        Assertions.assertNotEquals(info1, info3);
        Assertions.assertEquals(info1.hashCode(), info2.hashCode());
    }

    @Test
    void testMaxConnectionSetting() {
        int newMaxConnections = 10;
        connectionPool.setMaxConnection(newMaxConnections);
        Assertions.assertEquals(newMaxConnections, connectionPool.getMaxConnection());
    }

    @Test
    void testShutdownIdempotency() {
        // Should be safe to call shutdown multiple times
        connectionPool.shutdown();
        connectionPool.shutdown();

        Assertions.assertEquals(0, connectionPool.getLiveConnCount());
        Assertions.assertEquals(0, connectionPool.getConnPoolSize());
    }
}
