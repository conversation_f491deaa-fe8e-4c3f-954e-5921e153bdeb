# SFTP Connection Pool 并发问题修复

## 问题概述

原始的 SFTPConnectionPool 在并发写 SFTP 时存在以下问题：
1. **Connection Reset**: 网络超时和连接池耗尽导致的连接重置
2. **JSchException: connection is closed by foreign host**: 服务器主动断开连接

## 主要问题分析

### 1. 连接池管理的线程安全问题
- **问题**: `getFromPool` 方法中 `idleConnections.remove(info)` 移除了整个连接集合而不是单个连接
- **影响**: 导致连接泄漏和池管理混乱

### 2. 连接状态验证不充分
- **问题**: 缺乏有效的连接健康检查机制
- **影响**: 使用已断开的连接导致异常

### 3. 缺乏重试和超时机制
- **问题**: 没有连接超时设置和重试逻辑
- **影响**: 网络抖动时连接失败

### 4. 并发访问竞争条件
- **问题**: 使用非线程安全的 HashMap 和原始 int 计数器
- **影响**: 并发环境下数据不一致

## 解决方案

### 1. 改进连接池管理
```java
// 使用线程安全的数据结构
private ConcurrentHashMap<ConnectionInfo, HashSet<ChannelSftp>> idleConnections;
private ConcurrentHashMap<ChannelSftp, ConnectionInfo> con2infoMap;
private AtomicInteger liveConnectionCount;

// 修复连接获取逻辑
synchronized ChannelSftp getFromPool(ConnectionInfo info) {
    // 只移除单个连接，不是整个集合
    it.remove(); // 而不是 idleConnections.remove(info)
    
    // 验证连接有效性
    if (isConnectionValid(channel)) {
        return channel;
    } else {
        cleanupInvalidConnection(channel);
    }
}
```

### 2. 增强连接验证机制
```java
private boolean isConnectionValid(ChannelSftp channel) {
    try {
        return channel != null && 
               channel.isConnected() && 
               channel.getSession() != null &&
               channel.getSession().isConnected() &&
               !channel.isClosed();
    } catch (Exception e) {
        return false;
    }
}
```

### 3. 添加超时和重试机制
```java
// 连接超时配置
private static final int DEFAULT_CONNECTION_TIMEOUT = 30000; // 30秒
private static final int DEFAULT_SESSION_TIMEOUT = 60000;    // 60秒
private static final int MAX_RETRY_ATTEMPTS = 3;

// 重试逻辑
private ChannelSftp connectWithRetry(String host, int port, String user, 
                                    String password, String keyFile, int maxRetries) {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return connectInternal(host, port, user, password, keyFile);
        } catch (IOException e) {
            if (attempt < maxRetries) {
                Thread.sleep(RETRY_DELAY_MS * attempt);
            }
        }
    }
}
```

### 4. 改进连接生命周期管理
```java
// 连接操作包装器
private <T> T executeWithConnection(ConnectionOperation<T> operation) throws IOException {
    ChannelSftp channel = connect();
    try {
        return operation.execute(channel);
    } finally {
        disconnect(channel);
    }
}

// 函数式接口
@FunctionalInterface
private interface ConnectionOperation<T> {
    T execute(ChannelSftp channel) throws IOException;
}
```

### 5. 增强错误处理和清理
```java
private void cleanupInvalidConnection(ChannelSftp channel) {
    if (channel != null) {
        ConnectionInfo info = con2infoMap.remove(channel);
        if (info != null) {
            liveConnectionCount.decrementAndGet();
            // 从空闲连接中移除
            // 强制关闭连接
        }
    }
}
```

## 改进效果

### 1. 线程安全性
- 使用 `ConcurrentHashMap` 和 `AtomicInteger` 确保并发安全
- 细粒度同步减少锁竞争

### 2. 连接可靠性
- 连接验证机制确保使用有效连接
- 自动清理无效连接防止资源泄漏

### 3. 网络容错性
- 连接超时设置防止长时间阻塞
- 重试机制处理临时网络问题

### 4. 资源管理
- 改进的连接生命周期管理
- 确保连接及时归还到池中

## 使用建议

### 1. 配置优化
```properties
# 建议的连接池配置
fs.sftp.connection.max=10
# 根据并发需求调整最大连接数
```

### 2. 监控指标
- 监控 `getLiveConnCount()` 和 `getIdleCount()`
- 关注连接创建失败的日志

### 3. 错误处理
- 捕获并处理 `IOException` 和 `JSchException`
- 实现适当的重试策略

## 测试验证

创建了 `SFTPConnectionPoolTest` 来验证：
- 基本连接池操作
- 并发连接请求处理
- 连接信息相等性
- 关闭操作的幂等性

这些改进显著提高了 SFTP 连接池在高并发场景下的稳定性和可靠性。
